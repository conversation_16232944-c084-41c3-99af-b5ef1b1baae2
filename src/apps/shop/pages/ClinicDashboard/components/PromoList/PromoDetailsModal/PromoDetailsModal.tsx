import { useState, useCallback, useEffect } from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import styles from './PromoDetailsModal.module.css';
import { PromoTitle } from '../PromoTitle/PromoTitle';
import { PromoType } from '@/types/common';
import { OfferType } from '@/types';
import { PromoOfferItem } from '../PromoOfferItem/PromoOfferItem';
import { Subtotal } from '../Subtotal/Subtotal';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { getFreeItemsQty } from '@/libs/promotions/utils/promotionUtils';

type PromoOfferModalOptions = ModalOptionProps & {
  promoType: PromoType['type'];
  title: string;
  offers: PromoType['offers'];
  benefits: PromoType['benefits'];
  requirements: PromoType['requirements'];
};

type LocalCartItem = {
  offerId: string;
  offer: OfferType;
  quantity: number;
};

export const PromoDetailsModal = () => {
  const { addToCart, updatingProductIds } = useCartStore();
  const cartProductMapState = useCartProductMapState();
  const [localCartItems, setLocalCartItems] = useState<
    Map<string, LocalCartItem>
  >(new Map());
  const { modalOption, openModal } = useModalStore();
  const {
    benefits = [],
    requirements = [],
    offers = [],
    promoType,
    title,
  } = modalOption as PromoOfferModalOptions;

  const currentSubtotal = Array.from(localCartItems.values()).reduce(
    (total, item) => {
      const { salePrice } = getProductOfferComputedData(item.offer);
      if (!salePrice) return total;
      return total + salePrice * item.quantity;
    },
    0,
  );

  const totalCartQuantity = Array.from(localCartItems.values()).reduce(
    (total, item) => total + item.quantity,
    0,
  );

  const freeItemsCount = getFreeItemsQty({
    benefits,
    requirements,
    totalCartQuantity,
  });

  const getOriginalPrice = useCallback(() => {
    if (totalCartQuantity === 0 || freeItemsCount === 0) return currentSubtotal;

    const freeBenefit = benefits.find(
      (benefit) => benefit.type === 'give_free_product',
    );

    if (!freeBenefit || !freeBenefit.freeProductOffer) return currentSubtotal;

    const originalPrice =
      currentSubtotal +
      freeItemsCount *
        getProductOfferComputedData(freeBenefit.freeProductOffer).salePrice;

    return originalPrice;
  }, [benefits, currentSubtotal, freeItemsCount, totalCartQuantity]);

  const handleAddAllToCart = useCallback(() => {
    const itemsArray = Array.from(localCartItems.values());
    if (!itemsArray || itemsArray.length === 0) return;

    const offers = itemsArray.map((item) => {
      const currentQuantityInCart =
        cartProductMapState[item.offerId]?.quantity ?? 0;
      const newTotalQuantity = currentQuantityInCart + item.quantity;

      return {
        productOfferId: item.offerId,
        quantity: newTotalQuantity,
      };
    });

    addToCart({
      offers,
      onError: (message: string) => {
        console.error('Failed to add item to cart:', message);
      },
    });

    openModal({
      name: MODAL_NAME.PROMO_MATCHER_CONGRATS,
      title,
      savings: getOriginalPrice() - currentSubtotal,
    });
  }, [
    title,
    currentSubtotal,
    getOriginalPrice,
    localCartItems,
    cartProductMapState,
    openModal,
    addToCart,
  ]);

  const isLoading = Array.from(localCartItems.keys()).some((offerId) =>
    updatingProductIds.has(offerId),
  );

  const updateLocalCartItem = (offer: OfferType, quantity: number) => {
    setLocalCartItems((prev) => {
      const newItems = new Map(prev);
      newItems.set(offer.id, {
        offerId: offer.id,
        offer,
        quantity,
      });
      return newItems;
    });
  };

  useEffect(() => {
    if (!offers) return;
    const initialItems = offers.map((offer) => ({
      offerId: offer.id,
      offer,
      quantity: offer.increments,
    }));
    setLocalCartItems(
      new Map(initialItems.map((item) => [item.offerId, item])),
    );
  }, [offers]);

  if (!offers) return null;

  return (
    <Modal
      name={MODAL_NAME.PROMO_MATCHER_PRODUCTS}
      size="auto"
      withCloseButton
      customClasses={{ header: styles.modalHeader, body: styles.modalBody }}
    >
      <div className="flex flex-col">
        <div className="flex-col bg-white p-6 pt-0">
          <h3 className="mb-2 text-2xl font-medium">
            You&apos;re Almost There!
          </h3>
          <p className="mb-6 text-sm text-black/65">
            Follow the steps below to claim your savings before this offer
            expires.
          </p>
          <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
            <PromoTitle promoType={promoType} title={title} />
            <div className="divider-h"></div>
            <div className="grid gap-2">
              {offers.map((offer) => (
                <PromoOfferItem
                  key={offer.id}
                  offer={offer}
                  onQuantityChange={updateLocalCartItem}
                />
              ))}
            </div>
            <p className="mr-2 mb-4 inline-block text-sm">
              Buy now and get <strong>{freeItemsCount} for free!</strong>
            </p>
            <div className="divider-h"></div>
            <div className="flex items-center justify-between">
              <Subtotal
                subtotal={currentSubtotal}
                itemsCount={totalCartQuantity + freeItemsCount}
                originalPrice={getOriginalPrice()}
                showOriginalPrice={true}
              />
              <div className="w-52">
                <Button
                  className="w-full"
                  loading={isLoading}
                  disabled={localCartItems.size === 0}
                  onClick={handleAddAllToCart}
                >
                  Add to Cart
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};
