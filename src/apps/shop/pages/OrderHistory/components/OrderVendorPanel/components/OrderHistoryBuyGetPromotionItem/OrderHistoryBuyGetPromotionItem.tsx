import type { PromotionGroup } from '@/libs/orders/types';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import { OrderStatus } from '../../../OrderStatus/OrderStatus';
import { FEATURE_FLAGS } from '@/constants';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { PromoItemContent } from './PromoItemContent/PromoItemContent';

interface OrderHistoryPromoItemProps {
  promotionGroup: PromotionGroup;
}

export const OrderHistoryBuyGetPromotionItem = ({
  promotionGroup,
}: OrderHistoryPromoItemProps) => {
  const { promotion, items } = promotionGroup;

  if (!promotion || items.length === 0) return null;

  const firstItem = items[0];

  return (
    <div className="bg-gray-50">
      <div className="flex p-4">
        <p className="text-xs font-semibold">Promotion •</p>
        <p>{promotion.name}</p>
      </div>

      <div className="grid divide-y divide-gray-200/80">
        {items.map((item) => (
          <ProductCartHorizontal
            key={item.id}
            product={firstItem.product}
            productOfferId={firstItem.productOfferId || ''}
            content={<PromoItemContent item={item} />}
            actions={
              <div className="text-right">
                <p className="mb-1 text-xs font-medium text-gray-500/70">
                  Status
                </p>
                {item.status && (
                  <OrderStatus status={item.status} align="right" />
                )}
                {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
                  <Button className="mt-2 max-h-8 max-w-20">
                    <Icon name="cartSummary" className="mr-1.25" />
                    <Icon name="plus" size={'0.8rem'} />
                  </Button>
                )}
              </div>
            }
          />
        ))}
      </div>
    </div>
  );
};
