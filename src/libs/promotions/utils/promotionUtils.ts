import { CartItemType } from '@/libs/cart/types';
import { PromoType } from '@/types/common';

export function hasTriggeredPromotion(item: CartItemType): boolean {
  if (!item.product.promotions || item.product.promotions.length === 0) {
    return false;
  }

  const promotionDetails = getPromotionDetails({ item });
  return Boolean(promotionDetails?.wasTriggered);
}

export function getPromotionData(item: CartItemType) {
  const detail = getPromotionDetails({ item });

  if (!detail) {
    return { ...item, freeItemsQty: 0, freeOffer: null };
  }

  const { freeItemsQty, freeOffer } = detail;

  return {
    ...item,
    freeItemsQty,
    freeOffer: freeOffer ?? null,
  };
}

function getMinimumQuantity(requirements: PromoType['requirements']) {
  return (
    requirements.find((requirement) => requirement.type === 'minimum_quantity')
      ?.minimumQuantity || 1
  );
}

function getFreeBenefit(benefits: PromoType['benefits']) {
  return benefits?.find((benefit) => benefit.type === 'give_free_product');
}

export function getPromotionDetails({ item }: { item: CartItemType }) {
  const promotions: PromoType[] = item.product.promotions || [];
  const promo = promotions.find((p) => p.type === 'buy_x_get_y');
  if (!promo) return null;

  const minimumQuantity = getMinimumQuantity(promo.requirements);
  const freeBenefit = getFreeBenefit(promo.benefits);

  if (!freeBenefit) return null;

  const freeQtyPerTrigger = freeBenefit.quantity;
  const triggers = Math.floor(item.quantity / minimumQuantity);

  const freeItemsQty = triggers * freeQtyPerTrigger;

  const matchedOffer = item.product.offers.find(
    (offer) => offer.id === item.productOfferId,
  );
  const freeOffer = freeBenefit?.freeProductOffer ?? matchedOffer;

  return {
    promo,
    minimumQuantity,
    subtotalPaidItems: item.subtotal,
    subtotalAllItems:
      (item.quantity + freeItemsQty) * (Number(freeOffer?.price) * 1),
    freeItemsQty,
    freeOffer,
    manufacturer: item.product.manufacturer,
    imageUrl: item.product.imageUrl,
    wasTriggered: triggers > 0,
  };
}

export function groupTriggeredPromotionItems(
  items: CartItemType[],
): Record<string, { items: CartItemType[] }> {
  return items.reduce(
    (acc, item) => {
      if (hasTriggeredPromotion(item) && item.product.promotions) {
        item.product.promotions.forEach((promo) => {
          if (!acc[promo.type]) {
            acc[promo.type] = { items: [] };
          }
          acc[promo.type].items.push(item);
        });
      }
      return acc;
    },
    {} as Record<string, { items: CartItemType[] }>,
  );
}

export function getBuyXGetYPromotionDetails(items: CartItemType[]) {
  const firstItemWithPromo = items.find((item) => {
    const info = getPromotionDetails({ item });
    return info !== null && info.freeOffer !== undefined;
  });

  const defaultInfo = firstItemWithPromo
    ? getPromotionDetails({ item: firstItemWithPromo })
    : null;

  // If no valid promotion info is found, return a minimal structure
  if (!defaultInfo || !defaultInfo.freeOffer) {
    return {
      subtotalPaidItems: 0,
      subtotalAllItems: 0,
      paidItemsQty: 0,
      freeItemsQty: 0,
      promotion: null,
      freeOffer: null,
      manufacturer: '',
      imageUrl: '',
    };
  }

  return items.reduce(
    (acc, item) => {
      const info = getPromotionDetails({ item });
      if (!info || !info.freeOffer) return acc;

      return {
        subtotalPaidItems: acc.subtotalPaidItems + Number(item.subtotal || 0),
        subtotalAllItems: acc.subtotalAllItems + info.subtotalAllItems,
        paidItemsQty: acc.paidItemsQty + item.quantity,
        freeItemsQty: acc.freeItemsQty + info.freeItemsQty,
        promotion: info.promo,
        freeOffer: info.freeOffer,
        manufacturer: info.manufacturer || '',
        imageUrl: info.imageUrl || '',
      };
    },
    {
      subtotalPaidItems: 0,
      subtotalAllItems: 0,
      paidItemsQty: 0,
      freeItemsQty: 0,
      promotion: defaultInfo.promo,
      freeOffer: defaultInfo.freeOffer,
      manufacturer: defaultInfo.manufacturer || '',
      imageUrl: defaultInfo.imageUrl || '',
    },
  );
}

type FreeItemsQtyProps = {
  benefits: PromoType['benefits'];
  requirements: PromoType['requirements'];
  totalCartQuantity: number;
};
export function getFreeItemsQty({
  benefits,
  requirements,
  totalCartQuantity,
}: FreeItemsQtyProps): number {
  const minimumQuantity = getMinimumQuantity(requirements);
  const freeBenefit = getFreeBenefit(benefits);

  if (!freeBenefit) return 0;

  const triggers = Math.floor(totalCartQuantity / minimumQuantity);
  return triggers * freeBenefit.quantity;
}
